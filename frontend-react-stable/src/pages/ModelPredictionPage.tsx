import React, { useState, useEffect } from 'react';
import {
  Card,
  Radio,
  Upload,
  Input,
  Select,
  Button,
  Typography,
  Space,
  Divider,
  message,
  Spin,
  Alert,
  Statistic,
  Row,
  Col,
  Progress,
  Checkbox,
} from 'antd';
import { InboxOutlined, PlayCircleOutlined } from '@ant-design/icons';

import { modelPredictionAPI } from '../services/api';
import useTaskManager from '../hooks/useTaskManager';

const { Title, Text } = Typography;
const { Dragger } = Upload;
const { Option } = Select;

// 预测结果展示组件
const PredictionResultDisplay: React.FC<{ result: PredictionResult }> = ({ result }) => {


  return (
    <div>
      <Row gutter={16} style={{ marginBottom: 16 }}>
        <Col span={24}>
          <Statistic
            title="建议的流量清洗阈值 (pps)"
            value={result.suggested_threshold}
            precision={2}
            valueStyle={{ color: '#1890ff' }}
          />
          {result.suggested_threshold && (
            <Alert
              message="✅ 此阈值已自动保存到以输入CSV文件命名的结果文件中，可用于生成清洗模板。"
              type="success"
              showIcon
              style={{ marginTop: 8 }}
            />
          )}
        </Col>

      </Row>



      {/* 资源监控信息 - 一行内展示 */}
      {(result.duration_seconds !== undefined || result.cpu_percent !== undefined || result.memory_mb !== undefined || result.gpu_memory_mb !== undefined || result.gpu_utilization_percent !== undefined) && (
        <div style={{ marginBottom: 24 }}>
          <Title level={5}>资源使用情况</Title>
          <Row gutter={16}>
            {result.duration_seconds !== undefined && (
              <Col flex="auto">
                <Statistic
                  title="预测耗时"
                  value={result.duration_seconds}
                  precision={2}
                  suffix="秒"
                  valueStyle={{ color: '#1890ff' }}
                />
              </Col>
            )}
            {result.cpu_percent !== undefined && (
              <Col flex="auto">
                <Statistic
                  title="CPU使用率"
                  value={result.cpu_percent}
                  precision={1}
                  suffix="%"
                  valueStyle={{ color: '#52c41a' }}
                />
              </Col>
            )}
            {result.memory_mb !== undefined && (
              <Col flex="auto">
                <Statistic
                  title="内存使用"
                  value={result.memory_mb}
                  precision={1}
                  suffix="MB"
                  valueStyle={{ color: '#fa8c16' }}
                />
              </Col>
            )}
            {result.gpu_memory_mb !== undefined && (
              <Col flex="auto">
                <Statistic
                  title="GPU内存"
                  value={result.gpu_memory_mb}
                  precision={1}
                  suffix="MB"
                  valueStyle={{ color: '#722ed1' }}
                />
              </Col>
            )}
            {result.gpu_utilization_percent !== undefined && (
              <Col flex="auto">
                <Statistic
                  title="GPU利用率"
                  value={result.gpu_utilization_percent}
                  precision={1}
                  suffix="%"
                  valueStyle={{ color: '#eb2f96' }}
                />
              </Col>
            )}
          </Row>
        </div>
      )}

      {/* 模板生成信息 */}
      {result.template_info && (
        <div style={{ marginBottom: 24 }}>
          <Title level={5}>清洗模板生成</Title>
          <div style={{ padding: 12, backgroundColor: result.template_info.template_generated ? '#f6ffed' : '#fff2f0', border: `1px solid ${result.template_info.template_generated ? '#b7eb8f' : '#ffccc7'}`, borderRadius: 6 }}>
            {result.template_info.template_generated ? (
              <div>
                <p style={{ color: '#52c41a', fontWeight: 'bold', margin: 0 }}>✅ 清洗模板已自动生成</p>
                {result.template_info.template_path && (
                  <p style={{ margin: '8px 0 0 0' }}><strong>模板路径:</strong> {result.template_info.template_path}</p>
                )}
                {result.template_info.updated_thresholds !== undefined && (
                  <p style={{ margin: '4px 0 0 0' }}><strong>更新阈值数量:</strong> {result.template_info.updated_thresholds}</p>
                )}
              </div>
            ) : (
              <div>
                <p style={{ color: '#ff4d4f', fontWeight: 'bold', margin: 0 }}>❌ 清洗模板生成失败</p>
                {result.template_info.error && (
                  <p style={{ margin: '8px 0 0 0' }}><strong>错误信息:</strong> {result.template_info.error}</p>
                )}
              </div>
            )}
          </div>
        </div>
      )}

    </div>
  );
};

// 协议和数据类型配置（与Streamlit版本完全一致）
const protocolOptions = ['TCP', 'UDP', 'ICMP'];
const datatypeOptions = {
  TCP: ['spt_sip_dip', 'dpt_sip_dip', 'len_dpt_syn', 'seq_ack_dip'],
  UDP: ['spt_sip_dip', 'dpt_sip_dip'],
  ICMP: ['dip']
};

interface PredictionResult {
  model_name: string;
  suggested_threshold: number;
  message?: string;
  // 资源监控信息（与Streamlit版本一致）
  duration_seconds?: number;
  cpu_percent?: number;
  memory_mb?: number;
  gpu_memory_mb?: number;
  gpu_utilization_percent?: number;
  // 模板生成信息
  template_info?: {
    template_generated: boolean;
    template_path?: string;
    updated_thresholds?: number;
    error?: string;
  };
  result_path?: string;
}

const ModelPredictionPage: React.FC = () => {
  const [dataSource, setDataSource] = useState<'upload' | 'local'>('local');
  const [uploadedFile, setUploadedFile] = useState<any>(null);
  const [csvDir, setCsvDir] = useState('');
  const [availableCsvFiles, setAvailableCsvFiles] = useState<string[]>([]);
  const [selectedCsvFile, setSelectedCsvFile] = useState<string>('');
  const [csvFilesLoading, setCsvFilesLoading] = useState(false);

  // 模型相关状态
  const [modelDir, setModelDir] = useState('');
  const [availablePthFiles, setAvailablePthFiles] = useState<string[]>([]);
  const [selectedModels, setSelectedModels] = useState<string[]>([]);
  const [modelsLoading, setModelsLoading] = useState(false);
  const [predictionMode, setPredictionMode] = useState<'single' | 'multiple'>('single');

  // 单模型选择状态
  const [selectedModelFile, setSelectedModelFile] = useState<string>('');
  const [selectedParamsFile, setSelectedParamsFile] = useState<string>('');
  const [selectedScalerFile, setSelectedScalerFile] = useState<string>('');
  const [selectedProt, setSelectedProt] = useState<string>('');
  const [selectedDatatype, setSelectedDatatype] = useState<string>('');
  const [showManualSelection, setShowManualSelection] = useState(false);

  // 预测状态
  const [predicting, setPredicting] = useState(false);
  const [progress, setProgress] = useState(0);
  const [results, setResults] = useState<PredictionResult[]>([]);
  const [selectedResultIndex, setSelectedResultIndex] = useState<number>(0);
  const [matchingFilesLoading, setMatchingFilesLoading] = useState(false);
  const [autoGenerateTemplate, setAutoGenerateTemplate] = useState(false); // 新增：是否自动生成清洗模板

  // 任务管理
  const { submitPredictionTask, submitMultiPredictionTask, getCompletedTasksByType, fetchCompletedTasks } = useTaskManager();
  const [useAsyncPrediction, setUseAsyncPrediction] = useState(true); // 默认使用异步预测

  // 异步任务结果状态
  const [asyncPredictionResults, setAsyncPredictionResults] = useState<PredictionResult[]>([]);
  const [selectedAsyncTaskId, setSelectedAsyncTaskId] = useState<string>('');
  const [asyncTemplateInfo, setAsyncTemplateInfo] = useState<any>(null);
  const [selectedAsyncResultIndex, setSelectedAsyncResultIndex] = useState<number>(0);

  // 获取已完成的预测任务
  const completedPredictionTasks = getCompletedTasksByType('prediction');

  // 处理异步任务选择
  const handleAsyncTaskSelect = (taskId: string) => {
    setSelectedAsyncTaskId(taskId);
    const selectedTask = completedPredictionTasks.find(task => task.task_id === taskId);

    if (selectedTask && selectedTask.result) {
      // 检查是否是多模型异步预测结果
      if (selectedTask.result.is_multi_model && selectedTask.result.results) {
        // 多模型异步预测结果 - 直接使用 results 数组
        setAsyncPredictionResults(selectedTask.result.results);
        setAsyncTemplateInfo(selectedTask.result.template_info);
      } else {
        // 单模型异步预测结果 - 转换为数组格式
        const asyncResult: PredictionResult = {
          suggested_threshold: selectedTask.result.suggested_threshold || 0,
          model_name: selectedTask.result.model_name || '未知模型',
          message: selectedTask.result.message || '预测完成',
          duration_seconds: selectedTask.result.duration_seconds,
          cpu_percent: selectedTask.result.cpu_percent,
          memory_mb: selectedTask.result.memory_mb,
          gpu_memory_mb: selectedTask.result.gpu_memory_mb,
          gpu_utilization_percent: selectedTask.result.gpu_utilization_percent,
          template_info: selectedTask.result.template_info,
          result_path: selectedTask.result.result_path
        };

        setAsyncPredictionResults([asyncResult]);
        setAsyncTemplateInfo(selectedTask.result.template_info);
      }
    }
  };

  // 页面加载时获取已完成任务
  useEffect(() => {
    fetchCompletedTasks();
  }, [fetchCompletedTasks]);

  // 自动选择最新的预测任务
  useEffect(() => {
    if (completedPredictionTasks.length > 0 && !selectedAsyncTaskId) {
      const latestTask = completedPredictionTasks[completedPredictionTasks.length - 1];
      handleAsyncTaskSelect(latestTask.task_id);
    }
  }, [completedPredictionTasks, selectedAsyncTaskId, handleAsyncTaskSelect]);

  // 获取CSV文件列表
  const fetchCsvFiles = async () => {
    if (!csvDir) return;

    setCsvFilesLoading(true);
    try {
      const response = await modelPredictionAPI.listCsvFiles(csvDir);
      setAvailableCsvFiles(response.data.files || []);
    } catch (error: any) {
      message.error(error.response?.data?.detail || '获取CSV文件列表失败');
      setAvailableCsvFiles([]);
    } finally {
      setCsvFilesLoading(false);
    }
  };

  // 获取模型文件列表
  const fetchModelFiles = async () => {
    if (!modelDir) return;

    setModelsLoading(true);
    try {
      const response = await modelPredictionAPI.listModelFiles(modelDir);
      setAvailablePthFiles(response.data.pth_files || []);
    } catch (error: any) {
      message.error(error.response?.data?.detail || '获取模型文件列表失败');
      setAvailablePthFiles([]);
    } finally {
      setModelsLoading(false);
    }
  };

  // 自动匹配参数和标准化器文件（与Streamlit版本一致）
  const autoMatchFiles = async (modelFile: string) => {
    if (!modelFile || !modelDir) return;

    setMatchingFilesLoading(true);
    try {
      // 调用后端API获取匹配的文件和协议信息
      const response = await modelPredictionAPI.getMatchingFiles(modelFile, modelDir);

      if (response.data) {
        const matchingFiles = response.data;

        // 设置自动匹配的文件
        setSelectedParamsFile(matchingFiles.params_filename || '');
        setSelectedScalerFile(matchingFiles.scaler_filename || '');
        setSelectedProt(matchingFiles.protocol || '');
        setSelectedDatatype(matchingFiles.datatype || '');

        // 显示匹配结果
        if (matchingFiles.params_filename && matchingFiles.scaler_filename) {
          message.success('✅ 已自动匹配相关文件');
        }

        if (matchingFiles.protocol && matchingFiles.datatype) {
          message.success(`✅ 已自动识别模型对应的协议和数据类型：${matchingFiles.protocol} - ${matchingFiles.datatype}`);
          setShowManualSelection(false);
        } else {
          message.warning('⚠️ 无法从文件名自动识别协议和数据类型，请手动选择');
          setShowManualSelection(true);
        }
      }
    } catch (error: any) {
      message.error(error.response?.data?.detail || '获取匹配文件失败');
      // 如果API调用失败，回退到简单的文件名解析
      const baseNameWithoutExt = modelFile.replace('_model_best.pth', '');
      setSelectedParamsFile(`${baseNameWithoutExt}_params.json`);
      setSelectedScalerFile(`${baseNameWithoutExt}_scaler_y.pkl`);
      setShowManualSelection(true);
    } finally {
      setMatchingFilesLoading(false);
    }
  };

  // 处理模型文件选择
  const handleModelFileChange = (modelFile: string) => {
    setSelectedModelFile(modelFile);
    // 重置之前的选择
    setSelectedParamsFile('');
    setSelectedScalerFile('');
    setSelectedProt('');
    setSelectedDatatype('');
    setShowManualSelection(false);

    // 异步调用自动匹配
    autoMatchFiles(modelFile);
  };

  // 使用防抖来避免频繁请求 - 只在用户停止输入1.5秒后才发起请求
  useEffect(() => {
    if (dataSource === 'local' && csvDir && csvDir.length > 3) { // 至少输入4个字符才开始请求
      const timer = setTimeout(() => {
        fetchCsvFiles();
      }, 1500); // 1.5秒延迟，给用户足够时间输入完整路径

      return () => clearTimeout(timer);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [dataSource, csvDir]);

  useEffect(() => {
    if (modelDir && modelDir.length > 3) { // 至少输入4个字符才开始请求
      const timer = setTimeout(() => {
        fetchModelFiles();
      }, 1500); // 1.5秒延迟，给用户足够时间输入完整路径

      return () => clearTimeout(timer);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [modelDir]);

  // 文件上传配置
  const uploadProps = {
    name: 'file',
    multiple: false,
    accept: '.csv',
    beforeUpload: () => false,
    onChange: (info: any) => {
      if (info.fileList.length > 0) {
        setUploadedFile(info.fileList[0]);
      } else {
        setUploadedFile(null);
      }
    },
  };

  // 开始预测
  const handleStartPrediction = async () => {
    // 验证输入
    if (dataSource === 'upload' && !uploadedFile) {
      message.error('请上传CSV文件');
      return;
    }

    if (dataSource === 'local' && (!csvDir || !selectedCsvFile)) {
      message.error('请选择CSV文件');
      return;
    }

    // 验证模型选择
    let modelsToPredict: Array<{
      model_file: string;
      params_file: string;
      scaler_file: string;
      protocol: string;
      datatype: string;
    }> = [];

    if (predictionMode === 'single') {
      if (!selectedModelFile || !selectedProt || !selectedDatatype) {
        message.error('请选择模型文件并确保协议和数据类型已设置');
        return;
      }
      modelsToPredict = [{
        model_file: selectedModelFile,
        params_file: selectedParamsFile,
        scaler_file: selectedScalerFile,
        protocol: selectedProt,
        datatype: selectedDatatype
      }];
    } else {
      if (selectedModels.length === 0) {
        message.error('请至少选择一个模型');
        return;
      }

      // 为每个选中的模型获取匹配信息（与Streamlit版本一致）
      const validModels: Array<{
        model_file: string;
        params_file: string;
        scaler_file: string;
        protocol: string;
        datatype: string;
      }> = [];

      for (const modelFile of selectedModels) {
        try {
          const response = await modelPredictionAPI.getMatchingFiles(modelFile, modelDir);
          if (response.data) {
            const matchingFiles = response.data;
            const params_file = matchingFiles.params_filename;
            const scaler_file = matchingFiles.scaler_filename;
            const protocol = matchingFiles.protocol;
            const datatype = matchingFiles.datatype;

            // 只有当所有必要信息都可用时，才添加到选中模型列表
            if (params_file && scaler_file && protocol && datatype) {
              validModels.push({
                model_file: modelFile,
                params_file,
                scaler_file,
                protocol,
                datatype
              });
              message.success(`✅ 模型 ${modelFile} 已匹配: ${protocol} - ${datatype}`);
            } else {
              message.warning(`⚠️ 模型 ${modelFile} 无法自动识别所有必要参数，已跳过`);
            }
          } else {
            message.error(`获取模型 ${modelFile} 的匹配文件失败`);
          }
        } catch (error: any) {
          message.error(`处理模型 ${modelFile} 时出错: ${error.response?.data?.detail || error.message}`);
        }
      }

      if (validModels.length === 0) {
        message.error('没有可用的模型可以进行预测，请检查模型文件名是否包含必要的协议和数据类型信息');
        return;
      }

      modelsToPredict = validModels;
    }

    setPredicting(true);
    setProgress(0);
    setResults([]);

    try {
      if (useAsyncPrediction) {
        // 异步预测模式（支持单模型和多模型）
        const formData = new FormData();

        if (dataSource === 'upload' && uploadedFile) {
          formData.append('file', uploadedFile.originFileObj);
        } else {
          formData.append('csv_dir', csvDir);
          formData.append('selected_file', selectedCsvFile);
        }

        formData.append('model_dir', modelDir);
        formData.append('output_folder', modelDir);
        formData.append('auto_generate_template', autoGenerateTemplate.toString());

        let taskId: string | undefined;

        if (predictionMode === 'single') {
          // 单模型异步预测
          formData.append('model_filename', selectedModelFile);
          formData.append('params_filename', selectedParamsFile);
          formData.append('scaler_filename', selectedScalerFile);
          formData.append('selected_prot', selectedProt);
          formData.append('selected_datatype', selectedDatatype);

          taskId = await submitPredictionTask(formData);
        } else {
          // 多模型异步预测
          const modelsConfig = modelsToPredict.map(model => ({
            model_file: model.model_file,
            params_file: model.params_file,
            scaler_file: model.scaler_file,
            protocol: model.protocol,
            datatype: model.datatype
          }));

          formData.append('models_config', JSON.stringify(modelsConfig));
          taskId = await submitMultiPredictionTask(formData);
        }

        if (taskId) {
          const taskType = predictionMode === 'single' ? '预测任务' : '多模型预测任务';
          message.success(`${taskType}已启动，您可以继续使用其他功能，任务完成后会收到通知`);
          // 重置状态
          setPredicting(false);
          setProgress(0);
        }

        return; // 异步模式下直接返回
      }

      // 同步预测模式（保留原有逻辑）
      const allResults: PredictionResult[] = [];

      for (let i = 0; i < modelsToPredict.length; i++) {
        const model = modelsToPredict[i];

        // 更新进度
        setProgress(Math.round((i / modelsToPredict.length) * 90));

        if (modelsToPredict.length > 1) {
          message.info(`正在使用模型 ${i + 1}/${modelsToPredict.length}: ${model.model_file} 进行预测...`);
        }

        const formData = new FormData();

        if (dataSource === 'upload' && uploadedFile) {
          // 重新读取文件内容，因为文件内容只能读取一次
          formData.append('file', uploadedFile.originFileObj);
        } else {
          formData.append('csv_dir', csvDir);
          formData.append('selected_file', selectedCsvFile);
        }

        formData.append('model_filename', model.model_file);
        formData.append('params_filename', model.params_file);
        formData.append('scaler_filename', model.scaler_file);
        formData.append('selected_prot', model.protocol);
        formData.append('selected_datatype', model.datatype);
        formData.append('output_folder', modelDir);
        // 多模型预测时，所有模型都保存结果到同一个文件，只有最后一个模型生成模板
        formData.append('save_result_file', autoGenerateTemplate.toString()); // 如果要生成模板，所有模型都保存结果
        formData.append('auto_generate_template', (autoGenerateTemplate && i === modelsToPredict.length - 1).toString());

        const response = await modelPredictionAPI.predict(formData);

        if (response.data) {
          allResults.push({
            model_name: response.data.model_name || `${model.protocol}_${model.datatype}`,
            suggested_threshold: response.data.suggested_threshold || 0,
            message: response.data.message || '预测完成',
            // 添加资源监控信息
            duration_seconds: response.data.duration_seconds,
            cpu_percent: response.data.cpu_percent,
            memory_mb: response.data.memory_mb,
            gpu_memory_mb: response.data.gpu_memory_mb,
            gpu_utilization_percent: response.data.gpu_utilization_percent,
            // 添加模板信息（只有最后一个模型会有）
            template_info: response.data.template_info,
            result_path: response.data.result_path,
          });

          if (modelsToPredict.length > 1) {
            message.success(`✅ 模型 ${model.model_file} 预测成功`);

            // 如果是最后一个模型且生成了模板，显示模板生成信息
            if (i === modelsToPredict.length - 1 && response.data.template_info) {
              if (response.data.template_info.template_generated) {
                message.success({
                  content: (
                    <div>
                      <div style={{ fontWeight: 'bold', marginBottom: 4 }}>
                        🎉 清洗模板已自动生成！
                      </div>
                      <div style={{ fontSize: '12px', color: '#666' }}>
                        📁 文件路径: {response.data.template_info.template_path}<br/>
                        🔧 更新阈值: {response.data.template_info.updated_thresholds} 个
                      </div>
                    </div>
                  ),
                  duration: 6,
                });
              } else {
                message.error(`清洗模板生成失败: ${response.data.template_info.error}`);
              }
            }
          }
        }
      }

      setProgress(100);
      setResults(allResults);
      message.success(`✅ 共完成 ${allResults.length} 个模型的预测`);

    } catch (error: any) {
      message.error(error.response?.data?.detail || '预测失败');
    } finally {
      setPredicting(false);
    }
  };

  const isFormValid = () => {
    const hasData = dataSource === 'upload' ? uploadedFile : (csvDir && selectedCsvFile);

    if (predictionMode === 'single') {
      return hasData && selectedModelFile && selectedProt && selectedDatatype;
    } else {
      return hasData && selectedModels.length > 0;
    }
  };



  return (
    <div>
      <Title level={3} style={{ fontSize: '20px', fontWeight: 600, marginBottom: '8px' }}>模型实时预测与异常检测</Title>
      <Text type="secondary">
        加载已训练的流量模型，对新数据进行预测，并根据动态阈值检测异常流量。
      </Text>

      <Divider />

      {/* 流量数据源 */}
      <Card title="流量数据源" className="function-card">
        <Space direction="vertical" size="large" style={{ width: '100%' }}>
          <div>
            <Text strong>预测数据源：</Text>
            <Radio.Group
              value={dataSource}
              onChange={(e) => setDataSource(e.target.value)}
              style={{ marginTop: 8 }}
            >
              <Radio value="local">选择本地CSV文件</Radio>
              <Radio value="upload">上传CSV文件</Radio>
            </Radio.Group>
          </div>

          {/* 本地文件选择 */}
          {dataSource === 'local' && (
            <Space direction="vertical" style={{ width: '100%' }}>
              <div>
                <Text strong>CSV文件目录：</Text>
                <Input.Group compact style={{ marginTop: 8, display: 'flex' }}>
                  <Input
                    value={csvDir}
                    onChange={(e) => setCsvDir(e.target.value)}
                    placeholder="例如: /data/output"
                    style={{ flex: 1 }}
                  />
                  <Button
                    type="primary"
                    onClick={fetchCsvFiles}
                    loading={csvFilesLoading}
                    disabled={!csvDir}
                    style={{ marginLeft: 8 }}
                  >
                    刷新
                  </Button>
                </Input.Group>
              </div>

              <div>
                <Text strong>选择CSV文件：</Text>
                <Spin spinning={csvFilesLoading}>
                  <Select
                    value={selectedCsvFile}
                    onChange={setSelectedCsvFile}
                    placeholder="请选择CSV文件"
                    style={{ width: '100%', marginTop: 8 }}
                    loading={csvFilesLoading}
                  >
                    {availableCsvFiles.map((file) => (
                      <Option key={file} value={file}>
                        {file}
                      </Option>
                    ))}
                  </Select>
                </Spin>
              </div>
            </Space>
          )}

          {/* 文件上传 */}
          {dataSource === 'upload' && (
            <div>
              <Text strong>上传待预测的CSV文件：</Text>
              <Dragger {...uploadProps} style={{ marginTop: 8 }}>
                <p className="ant-upload-drag-icon">
                  <InboxOutlined />
                </p>
                <p className="ant-upload-text">点击或拖拽CSV文件到此区域上传</p>
                <p className="ant-upload-hint">
                  支持CSV格式的流量数据文件
                </p>
              </Dragger>
            </div>
          )}
        </Space>
      </Card>

      {/* 模型选择 */}
      <Card title="模型选择" className="function-card">
        <Space direction="vertical" size="large" style={{ width: '100%' }}>
          <div>
            <Text strong>模型目录：</Text>
            <Input.Group compact style={{ marginTop: 8, display: 'flex' }}>
              <Input
                value={modelDir}
                onChange={(e) => setModelDir(e.target.value)}
                placeholder="例如: /data/output"
                style={{ flex: 1 }}
              />
              <Button
                type="primary"
                onClick={fetchModelFiles}
                loading={modelsLoading}
                disabled={!modelDir}
                style={{ marginLeft: 8 }}
              >
                刷新
              </Button>
            </Input.Group>
          </div>

          <div>
            <Text strong>预测模式：</Text>
            <Radio.Group
              value={predictionMode}
              onChange={(e) => setPredictionMode(e.target.value)}
              style={{ marginTop: 8 }}
            >
              <Radio value="single">单个模型预测</Radio>
              <Radio value="multiple">多个模型批量预测</Radio>
            </Radio.Group>
          </div>

          {predictionMode === 'single' ? (
            <div>
              <Text strong>选择模型文件：</Text>
              <Spin spinning={modelsLoading}>
                <Select
                  value={selectedModelFile}
                  onChange={handleModelFileChange}
                  placeholder="选择一个训练好的模型文件，系统将自动匹配对应的参数和标准化器文件"
                  style={{ width: '100%', marginTop: 8 }}
                  loading={modelsLoading}
                >
                  {availablePthFiles.map((file) => (
                    <Option key={file} value={file}>
                      {file}
                    </Option>
                  ))}
                </Select>
              </Spin>

              {selectedModelFile && (
                <div style={{ marginTop: 16 }}>
                  <Space direction="vertical" style={{ width: '100%' }}>
                    <div>
                      <Text type="secondary">自动匹配的文件：</Text>
                      <Spin spinning={matchingFilesLoading}>
                        <div style={{ marginTop: 8, padding: 12, backgroundColor: '#f5f5f5', borderRadius: 4 }}>
                          {matchingFilesLoading ? (
                            <p>正在自动匹配相关文件...</p>
                          ) : (
                            <>
                              <p><strong>参数文件:</strong> {selectedParamsFile || '未匹配'}</p>
                              <p><strong>标准化器文件:</strong> {selectedScalerFile || '未匹配'}</p>
                              {!showManualSelection && selectedProt && selectedDatatype && (
                                <>
                                  <p><strong>协议:</strong> {selectedProt}</p>
                                  <p><strong>数据类型:</strong> {selectedDatatype}</p>
                                </>
                              )}
                            </>
                          )}
                        </div>
                      </Spin>
                    </div>

                    {showManualSelection && (
                      <div>
                        <Text strong>请手动选择协议和数据类型：</Text>
                        <div style={{ marginTop: 8 }}>
                          <Space direction="vertical" style={{ width: '100%' }}>
                            <Select
                              value={selectedProt}
                              onChange={setSelectedProt}
                              placeholder="选择与模型对应的协议"
                              style={{ width: '100%' }}
                            >
                              {protocolOptions.map((prot) => (
                                <Option key={prot} value={prot}>
                                  {prot}
                                </Option>
                              ))}
                            </Select>

                            {selectedProt && (
                              <Select
                                value={selectedDatatype}
                                onChange={setSelectedDatatype}
                                placeholder={`选择与模型对应的 ${selectedProt} 数据类型`}
                                style={{ width: '100%' }}
                              >
                                {(datatypeOptions[selectedProt as keyof typeof datatypeOptions] || []).map((datatype) => (
                                  <Option key={datatype} value={datatype}>
                                    {datatype}
                                  </Option>
                                ))}
                              </Select>
                            )}
                          </Space>
                        </div>
                      </div>
                    )}
                  </Space>
                </div>
              )}
            </div>
          ) : (
            <div>
              <Text strong>选择模型文件（多选）：</Text>
              <Spin spinning={modelsLoading}>
                <Select
                  mode="multiple"
                  value={selectedModels}
                  onChange={setSelectedModels}
                  placeholder="请选择多个模型文件进行批量预测"
                  style={{ width: '100%', marginTop: 8 }}
                  loading={modelsLoading}
                >
                  {availablePthFiles.map((file) => (
                    <Option key={file} value={file}>
                      {file}
                    </Option>
                  ))}
                </Select>
              </Spin>
            </div>
          )}

          {availablePthFiles.length === 0 && !modelsLoading && (
            <Alert
              message="未找到模型文件"
              description="请确保模型目录中包含训练好的模型文件（.pth）及其对应的参数文件和标准化器文件。"
              type="warning"
              showIcon
            />
          )}
        </Space>
      </Card>

      {/* 预测模式选择 */}
      <Card className="function-card" title="预测模式">
        <Space direction="vertical" size="middle" style={{ width: '100%' }}>
          <div>
            <Text strong>选择预测模式：</Text>
            <Radio.Group
              value={useAsyncPrediction}
              onChange={(e) => setUseAsyncPrediction(e.target.value)}
              style={{ marginTop: 8 }}
              // 现在多模型预测也支持异步了
            >
              <Radio value={true}>
                <Space>
                  异步预测（推荐）
                  <Text type="secondary" style={{ fontSize: 12 }}>
                    - 后台运行，不阻塞其他操作
                  </Text>
                </Space>
              </Radio>
              <Radio value={false}>
                <Space>
                  同步预测
                  <Text type="secondary" style={{ fontSize: 12 }}>
                    - 等待预测完成，期间无法使用其他功能
                  </Text>
                </Space>
              </Radio>
            </Radio.Group>
          </div>

          {useAsyncPrediction && (
            <Alert
              message="异步预测模式"
              description={
                <div>
                  预测任务将在后台运行，您可以继续使用系统的其他功能。
                  <br />
                  任务完成后会收到通知，可在 <strong>侧边栏菜单 → 任务管理</strong> 中查看进度和结果。
                </div>
              }
              type="info"
              showIcon
            />
          )}
        </Space>
      </Card>

      {/* 清洗模板生成选项 */}
      <Card className="function-card" title="清洗模板生成">
        <Space direction="vertical" size="middle" style={{ width: '100%' }}>
          <div>
            <Text strong>自动生成清洗模板：</Text>
            <div style={{ marginTop: 8 }}>
              <Checkbox
                checked={autoGenerateTemplate}
                onChange={(e) => setAutoGenerateTemplate(e.target.checked)}
              >
                预测完成后自动生成清洗模板
              </Checkbox>
            </div>
            <div style={{ marginTop: 8 }}>
              <Text type="secondary" style={{ fontSize: '12px' }}>
                选择此选项后，系统将在模型预测完成后自动调用清洗模板生成功能，
                根据预测结果中的阈值信息生成相应的清洗模板文件。
              </Text>
            </div>
          </div>
        </Space>
      </Card>

      {/* 开始预测按钮 */}
      <Card className="function-card">
        <Button
          type="primary"
          size="large"
          icon={<PlayCircleOutlined />}
          onClick={handleStartPrediction}
          loading={predicting}
          disabled={!isFormValid()}
          className="action-button"
        >
          {predicting ? '正在预测...' : '开始预测与检测'}
        </Button>

        {/* 预测进度 */}
        {predicting && (
          <div className="progress-section">
            <Text>预测进度：</Text>
            <Progress percent={progress} status="active" />
          </div>
        )}
      </Card>

      {/* 预测结果 */}
      {results.length > 0 && (
        <Card title="预测结果" className="function-card">
          {results.length > 1 ? (
            // 多模型结果展示 - 与Streamlit版本一致
            <div>
              <Divider />
              <Title level={4}>多模型预测结果</Title>

              {/* 显示模板生成状态 - 查找最后一个模型的模板信息 */}
              {(() => {
                const lastResult = results[results.length - 1];
                if (autoGenerateTemplate && lastResult?.template_info) {
                  return (
                    <div style={{ marginBottom: 16 }}>
                      {lastResult.template_info.template_generated ? (
                        <Alert
                          message="✅ 清洗模板已自动生成"
                          description={
                            <div>
                              <p style={{ margin: 0 }}><strong>模板路径:</strong> {lastResult.template_info.template_path}</p>
                              <p style={{ margin: '4px 0 0 0' }}><strong>更新阈值数量:</strong> {lastResult.template_info.updated_thresholds} 个</p>
                              <p style={{ margin: '4px 0 0 0', fontSize: '12px', color: '#666' }}>
                                基于 {results.length} 个模型的预测结果生成
                              </p>
                            </div>
                          }
                          type="success"
                          showIcon
                          style={{ marginBottom: 16 }}
                        />
                      ) : (
                        <Alert
                          message="❌ 清洗模板生成失败"
                          description={lastResult.template_info.error}
                          type="error"
                          showIcon
                          style={{ marginBottom: 16 }}
                        />
                      )}
                    </div>
                  );
                }
                return null;
              })()}

              <div style={{ marginBottom: 24 }}>
                <Text strong>选择要查看的模型结果：</Text>
                <Select
                  style={{ width: '100%', marginTop: 8 }}
                  placeholder="选择模型结果"
                  value={selectedResultIndex}
                  onChange={(value) => setSelectedResultIndex(value)}
                >
                  {results.map((result, index) => (
                    <Option key={index} value={index}>
                      {result.model_name}
                    </Option>
                  ))}
                </Select>
              </div>

              {/* 显示选中的模型结果 */}
              {results[selectedResultIndex] && (
                <div>
                  <Title level={5}>模型: {results[selectedResultIndex].model_name}</Title>
                  <PredictionResultDisplay result={results[selectedResultIndex]} />
                </div>
              )}
            </div>
          ) : (
            // 单模型结果展示
            <div>
              <Title level={4}>预测结果 - {results[0].model_name}</Title>
              <PredictionResultDisplay result={results[0]} />
            </div>
          )}
        </Card>
      )}

      {/* 异步预测结果展示 */}
      {completedPredictionTasks.length > 0 && (
        <Card title="异步预测结果" className="function-card" style={{ marginTop: 24 }}>
          <Alert
            message="异步预测已完成"
            description="以下是后台预测任务的结果，您可以查看预测结果。"
            type="success"
            showIcon
            style={{ marginBottom: 16 }}
          />
          <Space direction="vertical" size="large" style={{ width: '100%' }}>
            {/* 任务选择器 */}
            <div>
              <Text strong>选择预测任务：</Text>
              <Select
                value={selectedAsyncTaskId}
                onChange={handleAsyncTaskSelect}
                style={{ width: '100%', marginTop: 8 }}
                placeholder="请选择要查看的预测任务"
              >
                {completedPredictionTasks.map((task) => (
                  <Option key={task.task_id} value={task.task_id}>
                    {task.task_id.includes('_') ?
                      `${task.task_id.split('_')[0]} (${new Date(task.updated_at || task.created_at).toLocaleString()})` :
                      `任务 ${task.task_id.substring(0, 8)}... (${new Date(task.updated_at || task.created_at).toLocaleString()})`
                    }
                  </Option>
                ))}
              </Select>
            </div>

            {/* 结果展示 */}
            {asyncPredictionResults.length > 0 && (
              <div>
                {asyncPredictionResults.length > 1 ? (
                  // 多模型异步预测结果展示 - 与同步多模型预测保持一致
                  <div>
                    <Divider />
                    <Title level={4}>多模型预测结果</Title>

                    {/* 清洗模板生成信息 */}
                    {asyncTemplateInfo && (
                      <div style={{ marginBottom: 16 }}>
                        {asyncTemplateInfo.template_generated ? (
                          <Alert
                            message="✅ 清洗模板已自动生成"
                            description={
                              <div>
                                📁 文件路径: {asyncTemplateInfo.template_path}<br/>
                                🔧 更新阈值: {asyncTemplateInfo.updated_thresholds} 个
                              </div>
                            }
                            type="success"
                            showIcon
                            style={{ marginBottom: 16 }}
                          />
                        ) : (
                          <Alert
                            message="❌ 清洗模板生成失败"
                            description={asyncTemplateInfo.error}
                            type="error"
                            showIcon
                            style={{ marginBottom: 16 }}
                          />
                        )}
                      </div>
                    )}

                    <div style={{ marginBottom: 24 }}>
                      <Text strong>选择要查看的模型结果：</Text>
                      <Select
                        style={{ width: '100%', marginTop: 8 }}
                        placeholder="选择模型结果"
                        value={selectedAsyncResultIndex}
                        onChange={(value) => setSelectedAsyncResultIndex(value)}
                      >
                        {asyncPredictionResults.map((result, index) => (
                          <Option key={index} value={index}>
                            {result.model_name}
                          </Option>
                        ))}
                      </Select>
                    </div>

                    {/* 显示选中的模型结果 */}
                    {asyncPredictionResults[selectedAsyncResultIndex] && (
                      <div>
                        <Title level={5}>模型: {asyncPredictionResults[selectedAsyncResultIndex].model_name}</Title>
                        <PredictionResultDisplay result={asyncPredictionResults[selectedAsyncResultIndex]} />
                      </div>
                    )}
                  </div>
                ) : (
                  // 单模型异步预测结果展示
                  <div>
                    <Title level={4}>预测结果 - {asyncPredictionResults[0].model_name}</Title>
                    <PredictionResultDisplay result={asyncPredictionResults[0]} />
                  </div>
                )}
              </div>
            )}
          </Space>
        </Card>
      )}
    </div>
  );
};

export default ModelPredictionPage;
